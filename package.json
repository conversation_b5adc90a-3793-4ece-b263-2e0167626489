{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "concurrently": "^9.2.0", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}, "devDependencies": {"chokidar": "^4.0.3", "laravel-echo": "^2.1.6", "pusher-js": "^8.4.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.2", "@tailwindcss/oxide-linux-x64-gnu": "^4.1.11", "lightningcss-linux-x64-gnu": "^1.30.1"}, "devEngines": {"packageManager": {"name": "pnpm", "onFail": "warn"}, "runtime": {"name": "node", "onFail": "error"}}, "engines": {"node": ">=22.0.0", "pnpm": ">=10.0.0"}, "packageManager": "pnpm@10.13.1"}